# 编译生成的文件和目录
---
# 忽略所有 CMake 相关的临时文件和目录
**/CMakeFiles/
**/.cmake/
CMakeCache.txt
cmake_install.cmake
Makefile
Makefile2
*.dir/

# 忽略编译过程中产生的对象文件、调试信息等
*.o
*.d
*.ts
*.bin
*.out
moc_*.cpp*
uic_*.h
app_demo/Lib/rv1109/libneutron.so


# 打包输出目录
---
# 忽略整个打包输出目录，通常包含最终的发布件
pack_demo/out/

# 日志文件
---
# 忽略日志文件
*.log

app_demo/Src/BuildConfig.h

# 运行时/部署时生成的资源文件 (请根据实际情况调整)
---
# 如果pack_demo/files/application/neutron/res/web/ 整个目录都是生成的文件，直接忽略
pack_demo/files/application/neutron/res

# 忽略所有 .gz 压缩文件 (如果这些是临时文件或已包含在其他包中)
*.gz

# 其他可能需要忽略的特定路径 (如果它们不是源代码或需要版本控制的资源)
pack_demo/files/opt/resource
pack_demo/files/usr/bin
pack_demo/files/usr/lib
# 忽略所有 product 目录下各子目录中的内容
pack_demo/files/product/**
pack_demo/image_size.txt
