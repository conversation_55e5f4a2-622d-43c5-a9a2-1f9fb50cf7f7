/************************************************
 * Copyright(c) 2020 uni-ubi
 * 
 * Project:    UBase
 * FileName:   Version.h.in.h
 * Author:     shangyang
 * Email:      <EMAIL>
 * Version:    V1.0.0
 * Date:       2020-09-09 01:47
 * Description: 
 * Others:
 *************************************************/


#ifndef UBase_UBase_VERSION_H_IN_H
#define UBase_UBase_VERSION_H_IN_H

/**产品相关信息*/
#define PRODUCT_TYPE       "@PRODUCT_TYPE@"        /**产品类型*/
#define PRODUCT_MODE       "@PRODUCT_MODE@"        /**产品型号*/
#define PRODUCT_SUB_MODE   "@PRODUCT_SUB_MODE@"    /**产品子型号*/
#define PRODUCT_LANG       "@PRODUCT_LANG@"        /**产品语言*/
#define PRODUCT_VERNO      "@PRODUCT_VERNO@"       /**产品版本编号*/
#define PRODUCT_DEVTYPE    "@PRODUCT_DEVTYPE@"     /**产品设备类型*/
#define PRODUCT_TIMEZONE   "@PRODUCT_TIMEZONE@"    /**产品默认时区*/

/**编译信息*/
#define BUILD_DATA      "@BUILD_DATA@"        /**编译日期*/
#define BUILD_TIME      "@BUILD_TIME@"        /**编译时间*/
#define BUILD_VERSION   @BUILD_VERSION@       /**编译版本号*/
#define BUILD_BRANCH    "@BUILD_BRANCH@"      /**编译分支*/
#define VERSION_TYPE    "@VERSION_TYPE@"      /**版本类型*/

/**版本信息*/
#define VERSION_MAJOR   "@VERSION_MAJOR@"       /**主版本号*/
#define VERSION_MINOR   "@VERSION_MINOR@"       /**次版本号*/
#define VERSION_PATCH   "@VERSION_PATCH@"       /**修订本号*/

#define IS_CUSTOM_MODE(str) (!std::string(PRODUCT_SUB_MODE).empty() && std::string(PRODUCT_SUB_MODE).find(str) != std::string::npos)

#define CUSTOM_WEB_SENS 0x01
#define CUSTOM_WEB_SCREEN 0x02
#define IS_CUSTOM_WEB_MODE(val) ([]() -> bool { \
    std::string mode(PRODUCT_SUB_MODE); \
    size_t pos = mode.find("customweb"); \
    if (pos == std::string::npos) return false; \
    pos += 9; /* 跳过"customweb"字符串 */ \
    if (pos >= mode.length()) return false; \
    char digit_char = mode[pos]; \
    if (digit_char < '0' || digit_char > '9') return false; \
    int web_version = digit_char - '0'; \
    return ((val) & web_version) != 0; \
}()) 
#endif //UBase_UBase_VERSION_H_IN_H
