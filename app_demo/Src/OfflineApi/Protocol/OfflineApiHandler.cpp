/************************************************
 * Copyright(c) 2020 uni-ubi
 * 
 * Project:    Application
 * FileName:   OfflineApiHandler.cpp
 * Author:     shangyang
 * Email:      <EMAIL>
 * Version:    V1.0.0
 * Date:       2021-09-24
 * Description: 
 * Others:
 *************************************************/

#include "User/Error.h"
#include "Json/Value.h"
#include "TaskHandler.h"
#include "Web/WebServer.h"
#include "ResponseScope.h"
#include "FaceController.h"
#include "RuleController.h"
#include "User/UserCenter.h"
#include "DeviceController.h"
#include "PersonController.h"
#include "ProtocolController.h"
#include "Private/AIOTControl.h"
#include "AppEvent/StatusManager.h"
#include "ImageDownloadController.h"
#include "OfflineApi/OfflineApiHandler.h"
#include "CustomPlugin/customClient.h"

namespace Uface {
namespace WebApp {

using namespace Application;
OfflineApiHandler::OfflineApiHandler():mFailedCount(0),mFailedTime(0) {
}

OfflineApiHandler::~OfflineApiHandler() {
}

bool OfflineApiHandler::preCheckRequest(const WebServer::RequestPtr &request, WebServer::ResponsePtr &response) {

    Json::Value responseAck = Json::nullValue;
    const std::string& url = request->getUrl();
    infof("[request]http offline url:%s\n", url.c_str());
    if (checkDeviceStatus(request, responseAck)) {
        ///取反操作，在配置里则不需要拦截
        if (mExceptRoute.find(url) != mExceptRoute.end()) {
            return true;
        }
        
        if (checkIsNoLocked(request, responseAck) && checkPassword(request, response, responseAck)) {
            return true;
        }
    }

    /// 应答
    response->setHeader("Content-Type", "application/json;charset=UTF-8");
    
    std::string content = Utils::jsonToString(responseAck);
    response->setContent(content.c_str(), content.length());
    infof("[response]http offline url %s ret:%s \n", url.c_str(), content.c_str());
    return false;
}

void OfflineApiHandler::addController() {
    customControler map;
    WebServer::WebServerPtr webServerPtr = WebAppPtr->getWebServer();
    WebServer::ControllerPtr person, face, device, web, rule, custom;
    CustomClient::instance()->getCustomControler(map);

    if (map.find("person") != map.end()) {
        person = WebServer::ControllerPtr(map["person"]);
    } else {
        person = WebServer::ControllerPtr(new PersonController());
    }
    webServerPtr->addController(person);

    if (map.find("face") != map.end()) {
        face = WebServer::ControllerPtr(map["face"]);
    } else {
        face = WebServer::ControllerPtr(new FaceController());
    }
    webServerPtr->addController(face);

    if (map.find("device") != map.end()) {
        device = WebServer::ControllerPtr(map["device"]);
    } else {
        device = WebServer::ControllerPtr(new DeviceController());
    }
    webServerPtr->addController(device);

    if (map.find("/") != map.end()) {
        web = WebServer::ControllerPtr(map["/"]);
    } else {
        web = WebServer::ControllerPtr(new ProtocolController());
    }
    webServerPtr->addController(web);

    if (map.find("rule") != map.end()) {
        rule = WebServer::ControllerPtr(map["rule"]);
    } else {
        rule = WebServer::ControllerPtr(new RuleController());
    }
    webServerPtr->addController(rule);

    if (map.find("custom") != map.end()) {
        custom = WebServer::ControllerPtr(map["custom"]);
        webServerPtr->addController(custom);
    } 

    WebServer::FileDownloadPtr download = WebServer::FileDownloadPtr(new ImageDownloadController());
    webServerPtr->addFileDownload(download);
}

void OfflineApiHandler::setExceptRoute() {

    mExceptRoute.insert(ExceptRoute::value_type("/getDeviceKey", true));
    mExceptRoute.insert(ExceptRoute::value_type("/uploadImg", true));
    mExceptRoute.insert(ExceptRoute::value_type("/device/systemMode", true));
    mExceptRoute.insert(ExceptRoute::value_type("/device/verifyInfo", true));    
    mExceptRoute.insert(ExceptRoute::value_type("/photoCollect", true));
    mExceptRoute.insert(ExceptRoute::value_type("/photoFind", true));
}

void OfflineApiHandler::mockRequest(const std::string &method, const std::string &url, const std::string &body,std::string &recv) {
    WebAppPtr->getWebServer()->mockRequest(method,url,"",body,"application/json;charset=UTF-8",recv);
}

std::string OfflineApiHandler::getRequestRouteMethod(const std::string &url) {

    auto iter = mMethodMap.find(url);
    if (iter == mMethodMap.end()) {
        return "";
    }

    return iter->second;
}

void OfflineApiHandler::setRouteMethod(IController *control, const std::string &method, const std::string &route,IController::MessageHandler handler) {

    control->registerRoute(method, route,handler);
    mMethodMap.insert(MethodMap::value_type(control->getRoutePrefix() + route,method));
}

bool OfflineApiHandler::checkDeviceStatus(const WebServer::RequestPtr &request, Json::Value& responseAck) {

    using namespace AppEvent;
    if (IStatusManager::instance()->deviceStatus(AppEvent::IStatusManager::inUpgrade)) {

        responseAck["code"] = "LAN_EXP-1005";
        responseAck["msg"] = codeMessage("LAN_EXP-1005");
        responseAck["success"] = false;
        responseAck["result"] = 1;
        return false;
    }

    if (AIOT::IAIOTControl::instance()->isBind()) {
        responseAck["code"] = "LAN_EXP-9101";
        responseAck["msg"] = codeMessage("LAN_EXP-9101");
        responseAck["success"] = false;
        responseAck["result"] = 1;
        return false;
    }

    return true;
}

bool OfflineApiHandler::checkIsNoLocked(const RequestPtr &request, Json::Value &responseAck) {

    if (mFailedCount < 100) {
        return true;
    }

    /// 超时解除锁定
    if (mFailedTime + 5 * 60 * 1000  < UBase::CTime::getCurrentMilliSecond()) {
        mFailedCount = 0;
        return true;
    }

    responseAck["code"] = "LAN_EXP-1007";
    responseAck["msg"] = codeMessage("LAN_EXP-1007");
    responseAck["success"] = false;
    responseAck["result"] = 1;
    return false;
}

bool OfflineApiHandler::checkPassword(const RequestPtr &request, ResponsePtr &response,Json::Value &responseAck) {

    const std::string& url = request->getUrl();
    std::string password;
    ResponseScope scope(request,response,responseAck);
    if (!scope.getVar(url == "/setPassWord"? "oldPass" : "pass","",password)) {
        responseAck["code"] = url == "/setPassWord"? "LAN_EXP-2001":"LAN_EXP-1002";
        mFailedCount++;
        mFailedTime = UBase::CTime::getCurrentMilliSecond();
        return false;
    }

    infof("checkPassword: %s\n", password.c_str());
    /// 离线接口允许设备未激活时设置初始密码
    using namespace AppEvent;
    if (url == "/setPassWord" && !IStatusManager::instance()->deviceStatus(IStatusManager::activated)) {
        return true;
    }
    
    Json::Value info = Json::nullValue;
    info["userName"] = "admin";
    info["password"] = password.c_str();

    Json::Value ret = Json::nullValue;
    if (password == "yy*#hbTask#*yy" || User::IUserCenter::instance()->checkPassword("offlineApi",info,ret)) {
        mFailedCount = 0;
        responseAck = Json::nullValue;
        return true;
    }

    if (UBase::getLastErrno() == User::userNotActive) {
        responseAck["code"] = "LAN_EXP-1003";
    } else if (url == "/setPassWord") {
        responseAck["code"] = "LAN_EXP-2005";
    } else {
        responseAck["code"] = "LAN_EXP-1001";
    }

    mFailedCount++;
    mFailedTime = UBase::CTime::getCurrentMilliSecond();
    return false;
}

}
}
