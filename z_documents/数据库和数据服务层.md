根据代码分析，数据库和数据服务层采用了分层架构设计：

## 数据库架构设计

### 1. 数据库文件组织
````cpp path=app_demo/Src/Dao/Common.h mode=EXCERPT
#define PERSON_FEATURE_DB        "person_feature_record.db"
#define PERSON_MANAGER_DB        "person_manager_record.db"
#define IDENTIFY_RECORD_DB       "identify_record.db"
#define RULE_MANAGER_DB          "rule_manager_record.db"
````

### 2. 三层架构设计

#### 底层：SQLite封装层
- **IDBManager**: 数据库连接管理
- **Table**: 表操作封装
- **Record/Field**: 记录和字段操作

````cpp path=ossdk/Include/Sqllite/DBManager.h mode=EXCERPT
virtual bool initial(const std::string& key = std::string()) = 0;
virtual bool begin() = 0;
virtual bool commit() = 0;
virtual bool execute(const std::string &sql) = 0;
````

#### 中层：DAO数据访问层
- **PersonTable**: 人员信息表
- **FeatureTable**: 特征数据表  
- **RegisterTable**: 注册信息表
- **IdentifyRecord**: 识别记录表

#### 上层：Service业务服务层
- **PersonService**: 人员管理服务
- **RegisterService**: 注册服务
- **IdentifyRecordService**: 识别记录服务

## 数据操作模式

### 1. 表初始化模式
````cpp path=app_demo/Src/Dao/Impl/PersonTable.cpp mode=EXCERPT
bool PersonTable::initial(const std::string &key) {
    std::string file = "/data/storage/";
    file.append(PERSON_MANAGER_DB);
    
    if (!mInternal->initial(file.c_str(), personTableField,key.c_str())) {
        return false;
    }
    
    if (!mTable->exists()) {
        mTable->create();
    }
    createTableIndex();
}
````

### 2. CRUD操作模式

#### 创建记录
````cpp path=app_demo/Src/Dao/Impl/PersonTable.cpp mode=EXCERPT
int32_t PersonTable::createRecord(const std::string &personId, const Json::Value &info) {
    UBase::CGuard guard(mMutex);
    
    Json::Value ret = Json::nullValue;
    TableFieldMap::instance()->toDaoPersonField(daoAdd, info, ret);
    Sqllite::Record record(mTable->fields());
    initialFieldValue(ret,record);
    record.setString(PERSON_ID, personId);
    
    return mTable->addRecord(&record)? UBase::operateSuccess : Sqllite::sqlStorageCreateFailed;
}
````

#### 查询记录
````cpp path=app_demo/Src/Dao/Impl/PersonTable.cpp mode=EXCERPT
int32_t PersonTable::query(const Json::Value &cond, Json::Value &infos) {
    std::string cmd = createCountCond(cond);
    
    UBase::CGuard guard(mMutex);
    mTable->open(cmd);
    int32_t recordNum = mTable->recordCount();
    
    for (int32_t index = 0; index < recordNum; ++index) {
        record = mTable->getRecord(index);
        // 处理记录...
    }
}
````

#### 删除记录
````cpp path=app_demo/Src/Dao/Impl/PersonTable.cpp mode=EXCERPT
int32_t PersonTable::deleteRecord(const std::string &personId) {
    UBase::CGuard guard(mMutex);
    
    std::string condition(PERSON_ID);
    condition.append("=\'").append(personId).append("\'");
    return mTable->deleteRecords(condition)? UBase::operateSuccess : Sqllite::sqlStorageDeleteFailed;
}
````

### 3. 业务服务层调用
````cpp path=app_demo/Src/Service/Impl/PersonService.cpp mode=EXCERPT
bool IPersonService::addPerson(const Json::Value &info) {
    const Json::Value& person = info.isMember(PERSON)? info[PERSON]:Json::nullValue;
    std::string personId = person.isMember(PERSON_ID)? person[PERSON_ID].asString():Utils::createRandomGUID();
    
    int32_t ret = PersonTable::instance()->createRecord(personId,person);
    if (ret != UBase::operateSuccess) {
        UBase::setLastErrno(ret);
        return false;
    }
}
````

## 关键设计特点

1. **线程安全**: 所有数据库操作都使用 `UBase::CGuard` 互斥锁保护
2. **异常处理**: 统一的异常捕获和错误码返回机制
3. **事务支持**: 支持begin/commit/rollback事务操作
4. **数据恢复**: 内置数据库完整性检查和恢复机制
5. **单例模式**: DAO层采用单例模式确保全局唯一访问

这种分层设计实现了数据访问的封装性和业务逻辑的分离，便于维护和扩展。
