根据代码分析，这个配置系统的组织结构如下：

## 配置系统架构

### 1. 核心组件
- **AppConfig**: 主要配置管理接口，提供统一的配置访问入口
- **AppCfgInternal**: 内部配置管理实现，处理配置的保存、加载和事件通知
- **AppElemConfig**: 配置元素基类，各具体配置类继承此类

### 2. 配置存储路径
````cpp path=app_demo/Src/Config/Impl/AppConfig.cpp mode=EXCERPT
Json::Value info = Json::nullValue;
info["defConfigPath"] = "/opt/config/neutron";
info["configPath"]    = "/data/config/neutron";
````

### 3. 主要配置类型
- `hardware_config`: 硬件配置（防拆、触屏音效等）
- `device_config`: 设备配置（识别距离、音量等）
- `identifyModel`: 识别模型配置
- `alarm_config`: 报警配置
- `switch_plan_config`: 开关计划配置

## 如何修改设备配置

### 1. 通过AppConfig接口修改
````cpp path=app_demo/Src/Config/Impl/AppConfig.cpp mode=EXCERPT
bool AppConfig::setAppConfig(const char *useId, const char *name, const Json::Value &config) {
    // 验证配置并保存
    return internal->saveConfig(useId,iter->second,name,config);
}
````

### 2. 实际使用示例
````cpp path=app_demo/Src/Gui/Src/ui/pageSystemSetUpDistance.cpp mode=EXCERPT
Json::Value deviceCfg = AppConfig::instance()->getAppConfig("device_config");
deviceCfg["identifyDistance"] = distance*10;
AppConfig::instance()->setAppConfig("guiApp", "device_config", deviceCfg);
````

### 3. 配置修改流程
1. **获取当前配置**: 使用 `getAppConfig()` 获取现有配置
2. **修改配置值**: 更新JSON对象中的特定字段
3. **验证配置**: 系统自动调用 `verifyConfig()` 验证
4. **保存配置**: 调用 `setAppConfig()` 保存修改
5. **延迟写入**: 默认延迟2秒写入存储，避免频繁IO

### 4. 配置变更通知
````cpp path=app_demo/Src/Config/Impl/InternalConfig.cpp mode=EXCERPT
Json::Value info = Json::nullValue;
info["name"] = name;
IDeviceManager::instance()->sendEvent(clientId,"appConfig","saveAppConfig",info);
````

配置修改后会自动发送事件通知相关模块更新。
