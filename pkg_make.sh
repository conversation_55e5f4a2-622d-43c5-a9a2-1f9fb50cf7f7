#!/bin/bash

# 1，basic          基础版本，基于宇泛发布版本的重新编译版本
# 2，pubsec         基于基础版本， 增加视图库
# 3，sensreport     基于基础版本， coi增加异常未关门上报
# 4，customweb=xxx  基于基础版本， 增加自定义web 1：门磁配置 2：息屏配置
# 5，white          基于基础版本， 去除keda logo，纯英文版本
# 6，screenswitch   基于基础版本， 增加息屏模式 customweb2
# 7, prereport      基于基础版本， 使用紫橙模式，后端核验，算法结果后提前coi上报


compile_so=""
compile_pkg=""
compile_release="Release"
so_sub_mode=""
lang="zh"
timezone="+8"
debug=""
nomake=""
proj=""
dev_type=""

for arg in "$@"
do
    case $arg in
        so)
            compile_so="yes"
            ;;
        pkg)
            compile_pkg="pkg"
            ;;
        all)
            compile_so="yes"
            compile_pkg="pkg"
            ;;
        nomake)
            nomake="nomake"
            ;;
        sodebug)
            compile_release="Debug"
            ;;
        debug)
            so_sub_mode=${so_sub_mode}_"debug"
            ;;
        basic)
            # 基础版本，基于宇泛发布版本的重新编译版本
            ;;
        pubsec)
            # 基于基础版本 增加视图库
            so_sub_mode=${so_sub_mode}_"pubsec"
            ;;
        sensreport)
            # 基于基础版本 增加coi异常未关门上报
            so_sub_mode=${so_sub_mode}_"sensreport"
            ;;
        customweb=*)
            web="${arg#*=}"
            # 基于基础版本 增加自定义web
            so_sub_mode=${so_sub_mode}_"customweb${web}"
            ;;
        white)
            # 基于基础版本 去除keda logo
            so_sub_mode=${so_sub_mode}_"white"
            ;;
        screenswitch)
            # 基于基础版本 增加息屏模式
            so_sub_mode=${so_sub_mode}_"screenswitch"
            ;;
        proj=*)
            proj="${arg#*=}"
            echo "proj: ${proj}"
            # 根据不同proj参数设置不同配置
            case ${proj} in
                "agl")
                    # AGL项目配置
                    so_sub_mode=${so_sub_mode}_"projagl_multilingual"
                    lang="pt"
                    timezone="+1"
                    ;;
                "onlyen")
                    # 纯英文项目配置
                    so_sub_mode=${so_sub_mode}_"projonlyen"
                    lang="en"
                    timezone="+4"
                    ;;
                "auhwhiteen")
                    # 阿布扎比白牌纯英文项目配置
                    so_sub_mode=${so_sub_mode}_"projauhwhiteen"
                    lang="en"
                    timezone="+4"
                    dev_type="ADEye-FACV3"
                    ;;
                "whjwxp")
                    # 息屏模式项目配置
                    so_sub_mode=${so_sub_mode}_"projwhjwxp_screenswitch_customweb2"
                    ;;
                "hnjwsb")
                    # 海南纪委超时未关门的
                    so_sub_mode=${so_sub_mode}_"projhnjwsb_sensreport_customweb1"
                    ;;
                "seavan")
                    # seavan项目配置
                    so_sub_mode=${so_sub_mode}_"projseavan"
                    lang="en"
                    timezone="+4"
                    dev_type="SEA-A22-LN1-FC"
                    ;;
                "gxismp")
                    # gx定制对接ismp视图库项目配置
                    so_sub_mode=${so_sub_mode}_"projgxismp_pubsec"
                    ;;
                "zicheng")
                    # 后端核验，算法结果后提前coi上报
                    so_sub_mode=${so_sub_mode}_"projzicheng_prereport"
                    ;;
                *)
                    # 默认配置，不做特殊处理
                    ;;
            esac
            ;;
        *)
            echo "未知参数: $arg"
            ;;

    esac
done
# 1.编译
# 如果存在参数 app， 则编译
echo "======================================================================="
# 去除字符串开头的下划线
so_sub_mode=${so_sub_mode#_}
echo "          so_sub_mode:${so_sub_mode}"
echo "          lang:${lang}"
echo "          timezone:${timezone}"
echo "======================================================================="
# 1.1 编译app_demo
if [ -n "${compile_so}" ]; then
    cd app_demo
    ./compile.sh cmake sub_mode=${so_sub_mode} ${nomake} lang=${lang} timezone=${timezone} dev_type=${dev_type} ${compile_release}

    # 判断执行返回值
    if [ $? -ne 0 ]; then
        echo "========  compile.sh failed!  ========"
        exit 1
    fi

    # 打印17-38行
    awk 'NR>=17 && NR<=38' ./Src/BuildConfig.h
    cd -
fi
# 1.2 编译pkg
if [ -n "${compile_pkg}" ]; then
    cd pack_demo

    # 获取当前用户名
    current_user="$USER" # 或者使用 `whoami`，例如: current_user=$(whoami)

    # 判断当前用户是否是 maojunyi
    if [ "$current_user" = "maojunyi" ]; then
        # 如果当前用户是 maojunyi 'maojunyi' 作为 sudo 密码
        echo "maojunyi" | sudo -S ./feauture.sh ../pack_demo/yf/app/ ../pack_demo/ rv1109 "${compile_release}"
        echo "maojunyi" | sudo -S chmod 777 ../pack_demo/out/image/ -R
    else
        # 如果不是 maomao，则正常执行 sudo，会提示输入密码。
        # 你也可以在这里添加其他用户的密码，或者选择不自动输入密码。
        echo "当前用户不是 maojunyi sudo 密码或配置 sudoers。"
        sudo ./feauture.sh ../pack_demo/yf/app/ ../pack_demo/ rv1109 "${compile_release}"
        sudo chmod 777 ../pack_demo/out/image/ -R
    fi
    
    cd -
fi
